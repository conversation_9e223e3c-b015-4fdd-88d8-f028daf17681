#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chemosphere期刊文章数据抓取脚本
使用Selenium抓取https://www.sciencedirect.com/journal/chemosphere页面的文章数据
"""

import re
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup


class ChemosphereArticleScraper:
    def __init__(self):
        self.base_url = "https://www.sciencedirect.com/journal/chemosphere"
        self.driver = None
        self.setup_driver()

    def setup_driver(self):
        options = Options()
        # options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

        self.driver = webdriver.Chrome(options=options)

        self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
            Object.defineProperty(navigator, 'webdriver', {
              get: () => undefined
            });
            """
        })

    def extract_article_data(self, article_element):
        """
        从文章元素中提取数据
        根据提供的HTML结构提取标题、日期和链接
        """
        try:
            # 提取文章标题和链接
            title_link = article_element.find('a', class_='anchor js-article__item__title__link')
            if not title_link:
                return None

            # 获取文章链接
            href = title_link.get('href', '')

            # 获取文章标题
            title_span = title_link.find('span', class_='anchor-text')
            title = title_span.get_text(strip=True) if title_span else ''

            # 提取文章类型
            article_type_span = article_element.find('span', class_='js-article-subtype')
            article_type = article_type_span.get_text(strip=True) if article_type_span else ''

            # 提取开放获取信息
            open_access_span = article_element.find('span', class_='js-open-access')
            is_open_access = bool(open_access_span and 'Open access' in open_access_span.get_text())

            # 提取作者信息
            authors_div = article_element.find('div', class_='js-article__item__authors')
            authors = authors_div.get_text(strip=True) if authors_div else ''

            # 提取发表日期
            date_span = article_element.find('span', class_='js-article-item-date')
            publication_date = date_span.get_text(strip=True) if date_span else ''

            # 提取PDF链接
            pdf_link = article_element.find('a', class_='pdf-download')
            pdf_url = pdf_link.get('href', '') if pdf_link else ''

            # 提取DOI（如果存在）
            doi_div = article_element.find('div', {'hidden': ''})
            doi = doi_div.get_text(strip=True) if doi_div else ''

            return {
                'title': title,
                'article_type': article_type,
                'is_open_access': is_open_access,
                'authors': authors,
                'publication_date': publication_date,
                'article_url': href,
                'pdf_url': pdf_url,
                'doi': doi
            }

        except Exception as e:
            print(f"提取文章数据时出错: {e}")
            return None

    def scrape_articles(self):
        """直接抓取文章数据，无分页"""
        try:
            print("正在访问 Chemosphere 期刊页面...")
            self.driver.get(self.base_url)
            time.sleep(3)

            # 等待文章列表加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, "latest-published-articles"))
                )
                print("文章列表加载完成")
            except:
                print("等待文章列表超时，继续尝试解析页面")

            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            # 查找文章容器
            articles_container = soup.find('div', {'id': 'latest-published-articles'})
            if not articles_container:
                print("未找到文章容器")
                return []

            # 查找所有文章项
            article_items = articles_container.find_all('div', class_='js-article-item article-list-item')
            print(f"找到 {len(article_items)} 篇文章")

            articles = []
            for item in article_items:
                article_data = self.extract_article_data(item)
                if article_data:
                    articles.append(article_data)

            return articles

        except Exception as e:
            print(f"抓取文章时出错: {e}")
            return []

    def close(self):
        if self.driver:
            self.driver.quit()


def main():
    """主函数"""
    print("-" * 50)
    print("开始抓取 Chemosphere 期刊文章数据...")
    print("目标页面: https://www.sciencedirect.com/journal/chemosphere")
    print("-" * 50)

    scraper = ChemosphereArticleScraper()

    try:
        articles = scraper.scrape_articles()

        print(f"\n" + "=" * 50)
        print(f"抓取完成！总共获取到 {len(articles)} 篇文章")
        print("=" * 50)

        # 输出抓取到的文章数据
        for i, article in enumerate(articles, 1):
            print(f"\n文章 {i}:")
            print(f"标题: {article['title']}")
            print(f"类型: {article['article_type']}")
            print(f"开放获取: {'是' if article['is_open_access'] else '否'}")
            print(f"作者: {article['authors']}")
            print(f"发表日期: {article['publication_date']}")
            print(f"文章链接: {article['article_url']}")
            if article['pdf_url']:
                print(f"PDF链接: {article['pdf_url']}")
            if article['doi']:
                print(f"DOI: {article['doi']}")
            print("-" * 30)

        # 同时输出JSON格式
        print(f"\nJSON格式输出:")
        for article in articles:
            print(json.dumps(article, ensure_ascii=False, indent=2))

    finally:
        scraper.close()


if __name__ == "__main__":
    main()